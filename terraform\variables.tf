# General Configuration
variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "traksong"
}

variable "environment" {
  description = "Environment name (production, staging, development)"
  type        = string
  default     = "production"
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "af-south-1"  # Cape Town
}

variable "domain_name" {
  description = "Domain name for the application"
  type        = string
}

variable "ssl_certificate_arn" {
  description = "ARN of the SSL certificate in ACM"
  type        = string
}

# Network Configuration
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

# ECS Configuration
variable "api_min_capacity" {
  description = "Minimum number of API tasks"
  type        = number
  default     = 10
}

variable "api_max_capacity" {
  description = "Maximum number of API tasks"
  type        = number
  default     = 1000
}

variable "api_desired_capacity" {
  description = "Desired number of API tasks"
  type        = number
  default     = 20
}

variable "api_cpu" {
  description = "CPU units for API tasks (1024 = 1 vCPU)"
  type        = number
  default     = 2048
}

variable "api_memory" {
  description = "Memory for API tasks in MB"
  type        = number
  default     = 4096
}

variable "websocket_min_capacity" {
  description = "Minimum number of WebSocket tasks"
  type        = number
  default     = 5
}

variable "websocket_max_capacity" {
  description = "Maximum number of WebSocket tasks"
  type        = number
  default     = 500
}

variable "websocket_desired_capacity" {
  description = "Desired number of WebSocket tasks"
  type        = number
  default     = 10
}

variable "websocket_cpu" {
  description = "CPU units for WebSocket tasks"
  type        = number
  default     = 1024
}

variable "websocket_memory" {
  description = "Memory for WebSocket tasks in MB"
  type        = number
  default     = 2048
}

variable "worker_min_capacity" {
  description = "Minimum number of worker tasks"
  type        = number
  default     = 2
}

variable "worker_max_capacity" {
  description = "Maximum number of worker tasks"
  type        = number
  default     = 100
}

variable "worker_desired_capacity" {
  description = "Desired number of worker tasks"
  type        = number
  default     = 5
}

variable "worker_cpu" {
  description = "CPU units for worker tasks"
  type        = number
  default     = 1024
}

variable "worker_memory" {
  description = "Memory for worker tasks in MB"
  type        = number
  default     = 2048
}

# Auto Scaling Thresholds
variable "api_scale_up_threshold" {
  description = "CPU utilization threshold to scale up API"
  type        = number
  default     = 70
}

variable "api_scale_down_threshold" {
  description = "CPU utilization threshold to scale down API"
  type        = number
  default     = 30
}

variable "websocket_scale_up_threshold" {
  description = "CPU utilization threshold to scale up WebSocket"
  type        = number
  default     = 70
}

variable "websocket_scale_down_threshold" {
  description = "CPU utilization threshold to scale down WebSocket"
  type        = number
  default     = 30
}

# Redis Configuration
variable "redis_node_type" {
  description = "ElastiCache Redis node type"
  type        = string
  default     = "cache.r7g.xlarge"
}

variable "redis_num_nodes" {
  description = "Number of Redis cache nodes"
  type        = number
  default     = 3
}

variable "redis_parameter_group" {
  description = "Redis parameter group"
  type        = string
  default     = "default.redis7"
}

# MongoDB Atlas Configuration
variable "mongodb_atlas_project_id" {
  description = "MongoDB Atlas project ID"
  type        = string
}

variable "mongodb_instance_size" {
  description = "MongoDB Atlas instance size"
  type        = string
  default     = "M40"  # For high-volume production
}

variable "mongodb_disk_size" {
  description = "MongoDB Atlas disk size in GB"
  type        = number
  default     = 500
}

variable "mongodb_backup_enabled" {
  description = "Enable MongoDB Atlas backups"
  type        = bool
  default     = true
}

# Application Configuration
variable "jwt_secret" {
  description = "JWT secret key"
  type        = string
  sensitive   = true
}

variable "email_service" {
  description = "Email service provider"
  type        = string
  default     = "brevo"
}

variable "email_host" {
  description = "Email SMTP host"
  type        = string
  default     = "smtp-relay.brevo.com"
}

variable "email_port" {
  description = "Email SMTP port"
  type        = string
  default     = "587"
}

variable "email_user" {
  description = "Email SMTP username"
  type        = string
  sensitive   = true
}

variable "email_password" {
  description = "Email SMTP password"
  type        = string
  sensitive   = true
}

# Monitoring Configuration
variable "alert_email" {
  description = "Email address for alerts"
  type        = string
}
