import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Avatar,
  Divider,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  CircularProgress,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stack,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  AccountCircle as AccountIcon,
  Security as SecurityIcon,
  History as HistoryIcon,
  Store as StoreIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Visibility,
  VisibilityOff,
  Save as SaveIcon,
  Edit as EditIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  AccessTime as AccessTimeIcon,
  Devices as DevicesIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { accountService } from '../services/accountService';

const MaterialAccountManagement = () => {
  const { user, isAdmin, isCompliance, isSAMRO, isSAMPRA } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Define tab configuration based on user type
  const getTabConfig = () => {
    if (isAdmin || isCompliance) {
      return [
        { id: 'profile', label: 'Profile', icon: <AccountIcon /> },
        { id: 'settings', label: 'Settings', icon: <SettingsIcon /> },
        { id: 'security', label: 'Security', icon: <SecurityIcon /> }
      ];
    } else {
      return [
        { id: 'profile', label: 'Profile', icon: <AccountIcon /> },
        { id: 'store', label: 'Store Info', icon: <StoreIcon /> },
        { id: 'business', label: 'Business', icon: <BusinessIcon /> },
        { id: 'operations', label: 'Operations', icon: <ScheduleIcon /> },
        { id: 'settings', label: 'Settings', icon: <SettingsIcon /> },
        { id: 'security', label: 'Security', icon: <SecurityIcon /> }
      ];
    }
  };

  const tabConfig = getTabConfig();
  const currentTabId = tabConfig[activeTab]?.id;

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    username: '',
    email: '',
    organization: ''
  });

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Account activity state
  const [accountActivity, setAccountActivity] = useState([]);
  const [storeInfo, setStoreInfo] = useState(null);

  // Store form state
  const [storeForm, setStoreForm] = useState({
    name: '',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    phone: '',
    businessType: '',
    businessRegistrationNumber: '',
    vatNumber: '',
    timezone: 'Africa/Johannesburg'
  });

  // Operating hours state
  const [operatingHours, setOperatingHours] = useState({
    monday: { open: '09:00', close: '17:00', closed: false },
    tuesday: { open: '09:00', close: '17:00', closed: false },
    wednesday: { open: '09:00', close: '17:00', closed: false },
    thursday: { open: '09:00', close: '17:00', closed: false },
    friday: { open: '09:00', close: '17:00', closed: false },
    saturday: { open: '09:00', close: '17:00', closed: false },
    sunday: { open: '09:00', close: '17:00', closed: true }
  });

  // Settings state
  const [userSettings, setUserSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    theme: 'light',
    language: 'en',
    timezone: 'Africa/Johannesburg'
  });

  useEffect(() => {
    loadAccountData();
  }, []);

  const loadAccountData = async () => {
    setLoading(true);
    try {
      const [profileResponse, activityResponse] = await Promise.all([
        accountService.getProfile(),
        accountService.getActivity()
      ]);

      setProfileForm({
        username: profileResponse.data.username || '',
        email: profileResponse.data.email || '',
        organization: profileResponse.data.organization || ''
      });

      setAccountActivity(activityResponse.data || []);

      // Load store info if user is a store user
      if (user?.storeId && !isAdmin && !isCompliance) {
        const storeResponse = await accountService.getStoreInfo();
        const store = storeResponse.data;
        setStoreInfo(store);

        // Populate store form
        setStoreForm({
          name: store.name || '',
          address: store.address || '',
          city: store.city || '',
          province: store.province || '',
          postalCode: store.postalCode || '',
          phone: store.phone || '',
          businessType: store.businessType || '',
          businessRegistrationNumber: store.businessRegistrationNumber || '',
          vatNumber: store.vatNumber || '',
          timezone: store.timezone || 'Africa/Johannesburg'
        });

        // Populate operating hours if available
        if (store.openHours) {
          setOperatingHours(store.openHours);
        }
      }

      // Load user settings
      try {
        const settingsResponse = await accountService.getSettings();
        setUserSettings(settingsResponse.data || userSettings);
      } catch (settingsError) {
        console.log('Settings not available, using defaults');
      }

    } catch (error) {
      console.error('Failed to load account data:', error);
      setMessage({ type: 'error', text: 'Failed to load account information' });
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async () => {
    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      await accountService.updateProfile(profileForm);
      setMessage({ type: 'success', text: 'Profile updated successfully' });
    } catch (error) {
      console.error('Failed to update profile:', error);
      setMessage({ type: 'error', text: error.response?.data?.error || 'Failed to update profile' });
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', text: 'New passwords do not match' });
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      setMessage({ type: 'error', text: 'Password must be at least 8 characters long' });
      return;
    }

    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      await accountService.changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });

      setMessage({ type: 'success', text: 'Password changed successfully' });
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setShowPasswordDialog(false);
    } catch (error) {
      console.error('Failed to change password:', error);
      setMessage({ type: 'error', text: error.response?.data?.error || 'Failed to change password' });
    } finally {
      setSaving(false);
    }
  };

  const handleStoreUpdate = async () => {
    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      await accountService.updateStoreInfo({
        ...storeForm,
        openHours: operatingHours
      });
      setMessage({ type: 'success', text: 'Store information updated successfully' });
      // Reload store data
      loadAccountData();
    } catch (error) {
      console.error('Failed to update store info:', error);
      setMessage({ type: 'error', text: error.response?.data?.error || 'Failed to update store information' });
    } finally {
      setSaving(false);
    }
  };

  const handleSettingsUpdate = async () => {
    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      await accountService.updateSettings(userSettings);
      setMessage({ type: 'success', text: 'Settings updated successfully' });
    } catch (error) {
      console.error('Failed to update settings:', error);
      setMessage({ type: 'error', text: 'Failed to update settings' });
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getRoleDisplayName = () => {
    if (isAdmin) return 'Administrator';
    if (isCompliance) {
      if (isSAMRO) return 'SAMRO Staff';
      if (isSAMPRA) return 'SAMPRA Staff';
      return 'Compliance Officer';
    }
    return 'Store Manager';
  };

  const getOrganizationDisplayName = () => {
    if (isAdmin) return 'TrakSong Admin';
    if (isCompliance) return user?.organization || 'Compliance';
    return storeInfo?.name || 'Store';
  };

  const renderProfileTab = () => (
    <Grid container spacing={isMobile ? 2 : 3}>
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', p: isMobile ? 2 : 3 }}>
            <Avatar
              sx={{
                width: isMobile ? 60 : 80,
                height: isMobile ? 60 : 80,
                mx: 'auto',
                mb: 2,
                bgcolor: isAdmin ? 'primary.main' : isCompliance ? 'secondary.main' : 'success.main'
              }}
            >
              <PersonIcon sx={{ fontSize: isMobile ? 30 : 40 }} />
            </Avatar>
            <Typography variant={isMobile ? "body1" : "h6"} gutterBottom>
              {profileForm.username}
            </Typography>
            <Chip
              label={getRoleDisplayName()}
              color={isAdmin ? 'primary' : isCompliance ? 'secondary' : 'success'}
              sx={{ mb: 1 }}
              size={isMobile ? "small" : "medium"}
            />
            <Typography variant="body2" color="text.secondary">
              {getOrganizationDisplayName()}
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <AccountIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Profile Information</Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Username"
                  value={profileForm.username}
                  onChange={(e) => setProfileForm({ ...profileForm, username: e.target.value })}
                  disabled={saving}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={profileForm.email}
                  onChange={(e) => setProfileForm({ ...profileForm, email: e.target.value })}
                  disabled={saving}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Role"
                  value={getRoleDisplayName()}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Organization"
                  value={getOrganizationDisplayName()}
                  disabled
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleProfileUpdate}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button
                variant="outlined"
                startIcon={<SecurityIcon />}
                onClick={() => setShowPasswordDialog(true)}
              >
                Change Password
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderStoreInfoTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <StoreIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Store Information</Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Store Name"
                  value={storeForm.name}
                  onChange={(e) => setStoreForm({ ...storeForm, name: e.target.value })}
                  disabled={saving}
                  InputProps={{
                    startAdornment: <InputAdornment position="start"><StoreIcon /></InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={storeForm.phone}
                  onChange={(e) => setStoreForm({ ...storeForm, phone: e.target.value })}
                  disabled={saving}
                  InputProps={{
                    startAdornment: <InputAdornment position="start"><PhoneIcon /></InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  value={storeForm.address}
                  onChange={(e) => setStoreForm({ ...storeForm, address: e.target.value })}
                  disabled={saving}
                  multiline
                  rows={2}
                  InputProps={{
                    startAdornment: <InputAdornment position="start"><LocationIcon /></InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="City"
                  value={storeForm.city}
                  onChange={(e) => setStoreForm({ ...storeForm, city: e.target.value })}
                  disabled={saving}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Province"
                  value={storeForm.province}
                  onChange={(e) => setStoreForm({ ...storeForm, province: e.target.value })}
                  disabled={saving}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Postal Code"
                  value={storeForm.postalCode}
                  onChange={(e) => setStoreForm({ ...storeForm, postalCode: e.target.value })}
                  disabled={saving}
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleStoreUpdate}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Update Store Info'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderBusinessInfoTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <BusinessIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Business Details</Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth disabled>
                  <InputLabel>Business Type</InputLabel>
                  <Select value={storeForm.businessType} label="Business Type">
                    <MenuItem value="retail">Retail</MenuItem>
                    <MenuItem value="restaurant">Restaurant</MenuItem>
                    <MenuItem value="hotel">Hotel</MenuItem>
                    <MenuItem value="gym">Gym</MenuItem>
                    <MenuItem value="office">Office</MenuItem>
                    <MenuItem value="public_venue">Public Venue</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Business Registration Number"
                  value={storeForm.businessRegistrationNumber}
                  disabled
                  helperText="Contact administrator to update"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="VAT Number"
                  value={storeForm.vatNumber}
                  disabled
                  helperText="Contact administrator to update"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <CheckCircleIcon sx={{ mr: 1 }} />
              <Typography variant="h6">License Information</Typography>
            </Box>

            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="SAMRO License"
                  secondary={storeInfo?.licenses?.samro?.licenseNumber || 'Not registered'}
                />
                <Chip
                  label={storeInfo?.licenses?.samro?.isActive ? 'Active' : 'Inactive'}
                  color={storeInfo?.licenses?.samro?.isActive ? 'success' : 'default'}
                  size="small"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="secondary" />
                </ListItemIcon>
                <ListItemText
                  primary="SAMPRA License"
                  secondary={storeInfo?.licenses?.sampra?.licenseNumber || 'Not registered'}
                />
                <Chip
                  label={storeInfo?.licenses?.sampra?.isActive ? 'Active' : 'Inactive'}
                  color={storeInfo?.licenses?.sampra?.isActive ? 'success' : 'default'}
                  size="small"
                />
              </ListItem>

            </List>

            <Alert severity="info" sx={{ mt: 2 }}>
              License information is read-only. Contact compliance team for updates.
            </Alert>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderSecurityTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SecurityIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Security Settings</Typography>
            </Box>

            <List>
              <ListItem>
                <ListItemIcon>
                  <EmailIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Email Verification"
                  secondary={user?.emailVerified ? 'Verified' : 'Not verified'}
                />
                <Chip
                  label={user?.emailVerified ? 'Verified' : 'Pending'}
                  color={user?.emailVerified ? 'success' : 'warning'}
                  size="small"
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <SecurityIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Account Status"
                  secondary={user?.accountStatus || 'Active'}
                />
                <Chip
                  label={user?.accountStatus === 'active' ? 'Active' : user?.accountStatus}
                  color={user?.accountStatus === 'active' ? 'success' : 'warning'}
                  size="small"
                />
              </ListItem>
            </List>

            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => setShowPasswordDialog(true)}
              sx={{ mt: 2 }}
            >
              Change Password
            </Button>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <HistoryIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Recent Activity</Typography>
            </Box>

            <List>
              {accountActivity.slice(0, 5).map((activity, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemText
                      primary={activity.action}
                      secondary={formatDate(activity.timestamp)}
                    />
                  </ListItem>
                  {index < 4 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderOperationalTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <ScheduleIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Operating Hours</Typography>
            </Box>

            <Grid container spacing={2}>
              {Object.entries(operatingHours).map(([day, hours]) => (
                <Grid item xs={12} sm={6} md={4} key={day}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ textTransform: 'capitalize', flexGrow: 1 }}>
                          {day}
                        </Typography>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={!hours.closed}
                              onChange={(e) => setOperatingHours({
                                ...operatingHours,
                                [day]: { ...hours, closed: !e.target.checked }
                              })}
                              size="small"
                            />
                          }
                          label="Open"
                          labelPlacement="start"
                        />
                      </Box>
                      {!hours.closed && (
                        <Stack spacing={1}>
                          <TextField
                            fullWidth
                            label="Open"
                            type="time"
                            value={hours.open}
                            onChange={(e) => setOperatingHours({
                              ...operatingHours,
                              [day]: { ...hours, open: e.target.value }
                            })}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                          />
                          <TextField
                            fullWidth
                            label="Close"
                            type="time"
                            value={hours.close}
                            onChange={(e) => setOperatingHours({
                              ...operatingHours,
                              [day]: { ...hours, close: e.target.value }
                            })}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                          />
                        </Stack>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleStoreUpdate}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Update Hours'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <AccessTimeIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Timezone & Regional Settings</Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Timezone</InputLabel>
                  <Select
                    value={storeForm.timezone}
                    onChange={(e) => setStoreForm({ ...storeForm, timezone: e.target.value })}
                    label="Timezone"
                  >
                    <MenuItem value="Africa/Johannesburg">Africa/Johannesburg (SAST)</MenuItem>
                    <MenuItem value="Africa/Cairo">Africa/Cairo (EET)</MenuItem>
                    <MenuItem value="Africa/Lagos">Africa/Lagos (WAT)</MenuItem>
                    <MenuItem value="UTC">UTC</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderSettingsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SettingsIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Notification Settings</Typography>
            </Box>

            <Stack spacing={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={userSettings.emailNotifications}
                    onChange={(e) => setUserSettings({
                      ...userSettings,
                      emailNotifications: e.target.checked
                    })}
                  />
                }
                label="Email Notifications"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={userSettings.pushNotifications}
                    onChange={(e) => setUserSettings({
                      ...userSettings,
                      pushNotifications: e.target.checked
                    })}
                  />
                }
                label="Push Notifications"
              />
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SettingsIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Display Settings</Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={userSettings.theme}
                    onChange={(e) => setUserSettings({ ...userSettings, theme: e.target.value })}
                    label="Theme"
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={userSettings.language}
                    onChange={(e) => setUserSettings({ ...userSettings, language: e.target.value })}
                    label="Language"
                  >
                    <MenuItem value="en">English</MenuItem>
                    <MenuItem value="af">Afrikaans</MenuItem>
                    <MenuItem value="zu">Zulu</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
                onClick={handleSettingsUpdate}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Settings'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      <Typography variant={isMobile ? "h5" : "h4"} sx={{ fontWeight: 600, mb: 3 }}>
        Account Management
      </Typography>

      {message.text && (
        <Alert severity={message.type} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      <Paper sx={{ borderRadius: 2 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => {
            console.log('Tab changed to:', newValue, 'Tab ID:', tabConfig[newValue]?.id);
            setActiveTab(newValue);
          }}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            '& .MuiTab-root': {
              minHeight: isMobile ? 48 : 72,
              fontSize: isMobile ? '0.875rem' : '1rem'
            }
          }}
        >
          {tabConfig.map((tab, index) => (
            <Tab
              key={tab.id}
              label={isMobile ? tab.label : tab.label}
              icon={tab.icon}
              iconPosition={isMobile ? "start" : "top"}
            />
          ))}
        </Tabs>

        <Box sx={{ p: isMobile ? 2 : 3 }}>
          {/* Debug info */}
          {process.env.NODE_ENV === 'development' && (
            <Box sx={{ mb: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="caption">
                Debug: Active Tab: {activeTab}, Tab ID: {currentTabId}, User Type: {isAdmin ? 'Admin' : isCompliance ? 'Compliance' : 'Store'}
              </Typography>
            </Box>
          )}

          {currentTabId === 'profile' && renderProfileTab()}
          {currentTabId === 'store' && renderStoreInfoTab()}
          {currentTabId === 'business' && renderBusinessInfoTab()}
          {currentTabId === 'operations' && renderOperationalTab()}
          {currentTabId === 'settings' && renderSettingsTab()}
          {currentTabId === 'security' && renderSecurityTab()}
        </Box>
      </Paper>

      {/* Password Change Dialog */}
      <Dialog open={showPasswordDialog} onClose={() => setShowPasswordDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Change Password</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Current Password</InputLabel>
                <OutlinedInput
                  type={showCurrentPassword ? 'text' : 'password'}
                  value={passwordForm.currentPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        edge="end"
                      >
                        {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="Current Password"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>New Password</InputLabel>
                <OutlinedInput
                  type={showNewPassword ? 'text' : 'password'}
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        edge="end"
                      >
                        {showNewPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="New Password"
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Confirm New Password</InputLabel>
                <OutlinedInput
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label="Confirm New Password"
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPasswordDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handlePasswordChange}
            disabled={saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
          >
            {saving ? 'Changing...' : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialAccountManagement;
