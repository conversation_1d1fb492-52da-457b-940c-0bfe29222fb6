const WebSocket = require('ws');
const http = require('http');
const jwt = require('jsonwebtoken');
const Redis = require('redis');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Redis client for pub/sub
const redis = Redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

const redisSubscriber = redis.duplicate();

// Connect to Redis
redis.connect().catch(console.error);
redisSubscriber.connect().catch(console.error);

// Create HTTP server for health checks
const server = http.createServer((req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

// Create WebSocket server
const wss = new WebSocket.Server({ 
  server,
  verifyClient: (info) => {
    try {
      const token = new URL(info.req.url, 'http://localhost').searchParams.get('token');
      if (!token) return false;
      
      jwt.verify(token, process.env.JWT_SECRET);
      return true;
    } catch (error) {
      console.error('WebSocket authentication failed:', error.message);
      return false;
    }
  }
});

// Store active connections
const connections = new Map();
const storeConnections = new Map(); // storeId -> Set of connections

wss.on('connection', (ws, req) => {
  try {
    // Extract token and decode user info
    const token = new URL(req.url, 'http://localhost').searchParams.get('token');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const connectionId = generateConnectionId();
    const userInfo = {
      userId: decoded.userId,
      storeId: decoded.storeId,
      role: decoded.role,
      connectionId
    };
    
    // Store connection
    connections.set(connectionId, { ws, userInfo });
    
    // Group by store for efficient broadcasting
    if (userInfo.storeId) {
      if (!storeConnections.has(userInfo.storeId)) {
        storeConnections.set(userInfo.storeId, new Set());
      }
      storeConnections.get(userInfo.storeId).add(connectionId);
    }
    
    console.log(`WebSocket connected: User ${userInfo.userId}, Store ${userInfo.storeId}, Connection ${connectionId}`);
    
    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connected',
      payload: {
        connectionId,
        timestamp: new Date().toISOString()
      }
    }));
    
    // Handle incoming messages
    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data);
        await handleMessage(connectionId, message);
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
        ws.send(JSON.stringify({
          type: 'error',
          payload: { message: 'Invalid message format' }
        }));
      }
    });
    
    // Handle connection close
    ws.on('close', () => {
      console.log(`WebSocket disconnected: Connection ${connectionId}`);
      
      // Remove from store connections
      if (userInfo.storeId && storeConnections.has(userInfo.storeId)) {
        storeConnections.get(userInfo.storeId).delete(connectionId);
        if (storeConnections.get(userInfo.storeId).size === 0) {
          storeConnections.delete(userInfo.storeId);
        }
      }
      
      // Remove from connections
      connections.delete(connectionId);
    });
    
    // Handle connection errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for connection ${connectionId}:`, error);
    });
    
  } catch (error) {
    console.error('Error setting up WebSocket connection:', error);
    ws.close(1008, 'Authentication failed');
  }
});

// Handle incoming messages from clients
async function handleMessage(connectionId, message) {
  const connection = connections.get(connectionId);
  if (!connection) return;
  
  const { ws, userInfo } = connection;
  const { type, payload } = message;
  
  switch (type) {
    case 'ping':
      ws.send(JSON.stringify({ type: 'pong', payload: { timestamp: new Date().toISOString() } }));
      break;
      
    case 'subscribe_store_updates':
      // Subscribe to store-specific updates
      if (userInfo.storeId) {
        await redisSubscriber.subscribe(`store:${userInfo.storeId}:updates`);
      }
      break;
      
    case 'subscribe_admin_updates':
      // Admin users can subscribe to global updates
      if (userInfo.role === 'admin') {
        await redisSubscriber.subscribe('admin:global_updates');
      }
      break;
      
    case 'track_play_update':
      // Broadcast track play to store connections
      if (userInfo.storeId) {
        broadcastToStore(userInfo.storeId, {
          type: 'track_played',
          payload: {
            ...payload,
            timestamp: new Date().toISOString(),
            storeId: userInfo.storeId
          }
        });
      }
      break;
      
    default:
      console.log(`Unknown message type: ${type}`);
  }
}

// Broadcast message to all connections for a specific store
function broadcastToStore(storeId, message) {
  const storeConns = storeConnections.get(storeId);
  if (!storeConns) return;
  
  const messageStr = JSON.stringify(message);
  
  storeConns.forEach(connectionId => {
    const connection = connections.get(connectionId);
    if (connection && connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(messageStr);
    }
  });
}

// Broadcast message to all admin connections
function broadcastToAdmins(message) {
  const messageStr = JSON.stringify(message);
  
  connections.forEach(({ ws, userInfo }) => {
    if (userInfo.role === 'admin' && ws.readyState === WebSocket.OPEN) {
      ws.send(messageStr);
    }
  });
}

// Redis subscription handlers
redisSubscriber.on('message', (channel, message) => {
  try {
    const data = JSON.parse(message);
    
    if (channel.startsWith('store:')) {
      // Extract store ID from channel name
      const storeId = channel.split(':')[1];
      broadcastToStore(storeId, data);
    } else if (channel === 'admin:global_updates') {
      broadcastToAdmins(data);
    }
  } catch (error) {
    console.error('Error handling Redis message:', error);
  }
});

// Utility functions
function generateConnectionId() {
  return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Periodic cleanup of dead connections
setInterval(() => {
  connections.forEach((connection, connectionId) => {
    if (connection.ws.readyState === WebSocket.CLOSED) {
      connections.delete(connectionId);
    }
  });
}, 30000); // Clean up every 30 seconds

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, closing WebSocket server...');
  
  wss.clients.forEach(ws => {
    ws.close(1001, 'Server shutting down');
  });
  
  redis.disconnect();
  redisSubscriber.disconnect();
  
  server.close(() => {
    console.log('WebSocket server closed');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}/health`);
});
