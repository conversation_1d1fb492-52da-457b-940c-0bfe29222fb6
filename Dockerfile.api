# Multi-stage build for TrakSong API
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

WORKDIR /app

# Copy package files
COPY api/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Development stage
FROM base AS development
RUN npm ci
COPY api/ .
EXPOSE 5000
CMD ["npm", "run", "dev"]

# Production build stage
FROM base AS production-build
COPY api/ .
RUN npm run build 2>/dev/null || echo "No build script found"

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S traksong -u 1001

# Install production dependencies only
WORKDIR /app
COPY api/package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/*

# Copy application code
COPY --from=production-build --chown=traksong:nodejs /app .

# Create necessary directories
RUN mkdir -p uploads reports logs && \
    chown -R traksong:nodejs uploads reports logs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

USER traksong
EXPOSE 5000

CMD ["node", "server.js"]
