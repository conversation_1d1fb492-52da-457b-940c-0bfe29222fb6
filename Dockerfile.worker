# Background Worker Service Dockerfile
FROM node:18-alpine AS base

WORKDIR /app

# Install dependencies for worker processes
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev

# Copy package files
COPY api/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Development stage
FROM base AS development
RUN npm ci
COPY api/ .
CMD ["node", "worker.js"]

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S traksong -u 1001

WORKDIR /app

# Install production dependencies
COPY api/package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/*

# Copy worker code
COPY api/worker.js ./
COPY api/config/ ./config/
COPY api/models/ ./models/
COPY api/services/ ./services/
COPY api/jobs/ ./jobs/

# Create necessary directories
RUN mkdir -p logs reports && \
    chown -R traksong:nodejs logs reports

# Health check for worker service
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD node -e "console.log('Worker health check passed'); process.exit(0);"

USER traksong

CMD ["node", "worker.js"]
