# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist
build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Documentation
README.md
docs/

# Test files
test/
tests/
*.test.js
*.spec.js

# Temporary files
tmp/
temp/

# AWS and deployment files
aws/
terraform/
k8s/
.terraform/
*.tfstate
*.tfstate.backup

# Large media files (will be handled by S3)
uploads/tracks/*.mp3
uploads/tracks/*.wav
uploads/tracks/*.flac
