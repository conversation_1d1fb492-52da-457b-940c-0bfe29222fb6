import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Switch,
  CircularProgress,
  InputAdornment,
  Pagination,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  useMediaQuery,
  Collapse
} from '@mui/material';
import {
  Radio as RadioIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Language as WebIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useMedia } from '../context/MediaContext';
import { radioService as radioAPI } from '../services/api';
import { radioService } from '../services/radioService';

const MaterialRadioStations = () => {
  const { user, isAdmin } = useAuth();
  const { playRadio } = useMedia();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [loading, setLoading] = useState(true);
  const [stations, setStations] = useState([]);
  const [storeStations, setStoreStations] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [genreFilter, setGenreFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [stationsPerPage] = useState(isMobile ? 4 : 6);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [radioState, setRadioState] = useState({
    currentStation: null,
    isPlaying: false,
    volume: 0.75,
    isLoading: false
  });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingStation, setEditingStation] = useState(null);
  const [newStation, setNewStation] = useState({
    name: '',
    url: '',
    genre: '',
    country: '',
    description: ''
  });

  useEffect(() => {
    loadStations();

    // Initialize radio service
    radioService.initialize();

    // Subscribe to radio state changes
    const unsubscribe = radioService.subscribe((state) => {
      setRadioState(state);
    });

    return () => {
      unsubscribe();
    };
  }, [user?.storeId]);

  const loadStations = async () => {
    setLoading(true);
    try {
      // Load available stations and store-specific stations
      const [allStationsRes, storeStationsRes] = await Promise.all([
        radioAPI.getStations(),
        user?.storeId ? radioAPI.getStoreStations(user.storeId) : Promise.resolve({ data: [] })
      ]);

      setStations(allStationsRes.data || []);

      // Extract store station IDs from the response
      const storeStationIds = storeStationsRes.data?.map ?
        storeStationsRes.data.map(station => station._id) :
        storeStationsRes.data || [];
      setStoreStations(storeStationIds);
    } catch (error) {
      console.error('Failed to load radio stations:', error);
      setStations([]);
      setStoreStations([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddStation = async () => {
    try {
      const response = await radioAPI.addStation(newStation);
      setStations([...stations, response.data]);
      setDialogOpen(false);
      setNewStation({ name: '', url: '', genre: '', country: '', description: '' });
    } catch (error) {
      console.error('Failed to add station:', error);
      alert('Failed to add station. Please try again.');
    }
  };

  const handleEditStation = async () => {
    try {
      const response = await radioAPI.updateStation(editingStation._id, newStation);
      setStations(stations.map(s => s._id === editingStation._id ? response.data : s));
      setDialogOpen(false);
      setEditingStation(null);
      setNewStation({ name: '', url: '', genre: '', country: '', description: '' });
    } catch (error) {
      console.error('Failed to update station:', error);
      alert('Failed to update station. Please try again.');
    }
  };

  const handleDeleteStation = async (stationId) => {
    if (!confirm('Are you sure you want to delete this station?')) {
      return;
    }

    try {
      await radioAPI.deleteStation(stationId);
      setStations(stations.filter(s => s._id !== stationId));
    } catch (error) {
      console.error('Failed to delete station:', error);
      alert('Failed to delete station. Please try again.');
    }
  };

  const handleToggleStoreStation = async (stationId) => {
    try {
      const isCurrentlyEnabled = storeStations.includes(stationId);
      const newStoreStations = isCurrentlyEnabled
        ? storeStations.filter(id => id !== stationId)
        : [...storeStations, stationId];

      await radioAPI.setStoreStations(user.storeId, newStoreStations);
      setStoreStations(newStoreStations);
    } catch (error) {
      console.error('Failed to update store stations:', error);
      alert('Failed to update store stations. Please try again.');
    }
  };

  const handlePlayStation = async (station) => {
    if (radioState.currentStation?._id === station._id && radioState.isPlaying) {
      // Stop current station using force stop to avoid conflicts
      radioService.forceStop();
    } else {
      // Play new station using media context
      await playRadio(station);
    }
  };

  const openAddDialog = () => {
    setEditingStation(null);
    setNewStation({ name: '', url: '', genre: '', country: '', description: '' });
    setDialogOpen(true);
  };

  const openEditDialog = (station) => {
    setEditingStation(station);
    setNewStation({
      name: station.name,
      url: station.url,
      genre: station.genre,
      country: station.country,
      description: station.description
    });
    setDialogOpen(true);
  };

  // Filter stations
  const filteredStations = stations.filter(station => {
    const matchesSearch = station.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      station.genre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      station.country.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesGenre = genreFilter === 'all' || station.genre === genreFilter;

    return matchesSearch && matchesGenre;
  });

  // Pagination
  const totalPages = Math.ceil(filteredStations.length / stationsPerPage);
  const paginatedStations = filteredStations.slice(
    (page - 1) * stationsPerPage,
    page * stationsPerPage
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          Radio Stations
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Radio Stations
        </Typography>
        {isAdmin && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={openAddDialog}
          >
            Add Station
          </Button>
        )}
      </Box>

      {/* Search and Filter Controls */}
      {isMobile ? (
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            sx={{ mb: 2, width: '100%' }}
            startIcon={<SearchIcon />}
          >
            Search & Filter
          </Button>
          <Collapse in={showMobileFilters}>
            <Paper sx={{ p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Search stations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Genre</InputLabel>
                    <Select
                      value={genreFilter}
                      label="Genre"
                      onChange={(e) => setGenreFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Genres</MenuItem>
                      <MenuItem value="Jazz">Jazz</MenuItem>
                      <MenuItem value="Rock">Rock</MenuItem>
                      <MenuItem value="Pop">Pop</MenuItem>
                      <MenuItem value="Classical">Classical</MenuItem>
                      <MenuItem value="Electronic">Electronic</MenuItem>
                      <MenuItem value="Lounge">Lounge</MenuItem>
                      <MenuItem value="Ambient">Ambient</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Paper>
          </Collapse>
        </Box>
      ) : (
        <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search stations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ width: 300 }}
          />
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Genre</InputLabel>
            <Select
              value={genreFilter}
              label="Genre"
              onChange={(e) => setGenreFilter(e.target.value)}
            >
              <MenuItem value="all">All Genres</MenuItem>
              <MenuItem value="Jazz">Jazz</MenuItem>
              <MenuItem value="Rock">Rock</MenuItem>
              <MenuItem value="Pop">Pop</MenuItem>
              <MenuItem value="Classical">Classical</MenuItem>
              <MenuItem value="Electronic">Electronic</MenuItem>
              <MenuItem value="Lounge">Lounge</MenuItem>
              <MenuItem value="Ambient">Ambient</MenuItem>
            </Select>
          </FormControl>
        </Box>
      )}

      {/* Currently Playing */}
      {radioState.currentStation && (
        <Alert
          severity={radioState.isPlaying ? "success" : radioState.isLoading ? "info" : "warning"}
          sx={{ mb: 3 }}
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={() => radioService.forceStop()}
              disabled={radioState.isLoading}
            >
              <StopIcon />
            </IconButton>
          }
        >
          {radioState.isLoading ? (
            <>Loading: {radioState.currentStation.name}</>
          ) : radioState.isPlaying ? (
            <>Now playing: {radioState.currentStation.name}</>
          ) : (
            <>Stopped: {radioState.currentStation.name}</>
          )}
        </Alert>
      )}

      {/* Station Categories */}
      <Grid container spacing={isMobile ? 2 : 3}>
        {/* Available Stations */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: isMobile ? 2 : 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Available Stations
            </Typography>
            <Grid container spacing={isMobile ? 1 : 2}>
              {paginatedStations.map((station) => (
                <Grid item xs={12} sm={6} md={isMobile ? 12 : 4} key={station._id}>
                  <Card sx={{ height: '100%', borderRadius: 2 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <RadioIcon sx={{ color: 'primary.main', mr: 1 }} />
                        <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                          {station.name}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ mb: 2 }}>
                        <Chip 
                          label={station.frequency ? `${station.frequency} FM` : station.genre} 
                          size="small" 
                          color="primary" 
                          sx={{ mr: 1, mb: 1 }} 
                        />
                        <Chip 
                          label={station.country} 
                          size="small" 
                          variant="outlined" 
                          sx={{ mb: 1 }} 
                        />
                      </Box>
                      
                      <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                        {station.description}
                      </Typography>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <WebIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                          {station.url}
                        </Typography>
                      </Box>
                    </CardContent>
                    
                    <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => handlePlayStation(station)}
                          color={radioState.currentStation?._id === station._id && radioState.isPlaying ? 'secondary' : 'primary'}
                          disabled={radioState.isLoading && radioState.currentStation?._id === station._id}
                        >
                          {radioState.isLoading && radioState.currentStation?._id === station._id ? (
                            <CircularProgress size={20} />
                          ) : radioState.currentStation?._id === station._id && radioState.isPlaying ? (
                            <StopIcon />
                          ) : (
                            <PlayIcon />
                          )}
                        </IconButton>
                        {isAdmin && (
                          <>
                            <IconButton
                              size="small"
                              onClick={() => openEditDialog(station)}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteStation(station._id)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </>
                        )}
                      </Box>
                      
                      <Switch
                        checked={storeStations.includes(station._id)}
                        onChange={() => handleToggleStoreStation(station._id)}
                        size="small"
                      />
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Pagination */}
            {totalPages > 1 && (
              <Stack spacing={2} alignItems="center" sx={{ mt: 3 }}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                  size="small"
                />
              </Stack>
            )}
          </Paper>
        </Grid>

        {/* Store Configuration */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: isMobile ? 2 : 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Store Configuration
            </Typography>

            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Enabled Stations ({storeStations.length})
            </Typography>

            {storeStations.length > 0 ? (
              isMobile ? (
                <List>
                  {storeStations.map((stationId) => {
                    const station = stations.find(s => s._id === stationId);
                    if (!station) return null;

                    return (
                      <ListItem key={stationId} sx={{ px: 0 }}>
                        <ListItemIcon>
                          <RadioIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={station.name}
                          secondary={station.frequency}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            size="small"
                            onClick={() => handlePlayStation(station)}
                            color={radioState.currentStation?._id === station._id && radioState.isPlaying ? 'secondary' : 'default'}
                            disabled={radioState.isLoading && radioState.currentStation?._id === station._id}
                          >
                            {radioState.isLoading && radioState.currentStation?._id === station._id ? (
                              <CircularProgress size={16} />
                            ) : radioState.currentStation?._id === station._id && radioState.isPlaying ? (
                              <StopIcon />
                            ) : (
                              <PlayIcon />
                            )}
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    );
                  })}
                </List>
              ) : (
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Station</TableCell>
                        <TableCell>Frequency</TableCell>
                        <TableCell align="center">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                    {storeStations.map((stationId) => {
                      const station = stations.find(s => s._id === stationId);
                      if (!station) return null;

                      return (
                        <TableRow key={stationId}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <RadioIcon color="primary" sx={{ fontSize: 16 }} />
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {station.name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip label={station.frequency} size="small" color="primary" />
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              size="small"
                              onClick={() => handlePlayStation(station)}
                              color={radioState.currentStation?._id === station._id && radioState.isPlaying ? 'secondary' : 'default'}
                              disabled={radioState.isLoading && radioState.currentStation?._id === station._id}
                            >
                              {radioState.isLoading && radioState.currentStation?._id === station._id ? (
                                <CircularProgress size={16} />
                              ) : radioState.currentStation?._id === station._id && radioState.isPlaying ? (
                                <StopIcon sx={{ fontSize: 16 }} />
                              ) : (
                                <PlayIcon sx={{ fontSize: 16 }} />
                              )}
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
              )
            ) : (
              <Alert severity="info">
                No stations enabled for this store. Use the switches on station cards to enable them.
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Add/Edit Station Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingStation ? 'Edit Station' : 'Add New Station'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Station Name"
              value={newStation.name}
              onChange={(e) => setNewStation({ ...newStation, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Stream URL"
              value={newStation.url}
              onChange={(e) => setNewStation({ ...newStation, url: e.target.value })}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Genre</InputLabel>
              <Select
                value={newStation.genre}
                label="Genre"
                onChange={(e) => setNewStation({ ...newStation, genre: e.target.value })}
              >
                <MenuItem value="Jazz">Jazz</MenuItem>
                <MenuItem value="Rock">Rock</MenuItem>
                <MenuItem value="Pop">Pop</MenuItem>
                <MenuItem value="Classical">Classical</MenuItem>
                <MenuItem value="Electronic">Electronic</MenuItem>
                <MenuItem value="Lounge">Lounge</MenuItem>
                <MenuItem value="Ambient">Ambient</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Country"
              value={newStation.country}
              onChange={(e) => setNewStation({ ...newStation, country: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={newStation.description}
              onChange={(e) => setNewStation({ ...newStation, description: e.target.value })}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={editingStation ? handleEditStation : handleAddStation}
            variant="contained"
          >
            {editingStation ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialRadioStations;
