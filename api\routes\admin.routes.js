const express = require('express');
const router = express.Router();
const { authenticate, authorizeAdmin, authorizeCompliance, authorizeAuditTrail } = require('../middleware/auth.middleware');
const {
  getTopTracks,
  getStoreSummary,
  getAllStores,
  createStore,
  updateStore,
  deleteStore,
  getAllUsers,
  getAllUsersIncludingCompliance,
  createUser,
  updateUser,
  deleteUser,
  getAllTracksAdmin,
  getTrackDetails,
  generateTrackReport,
  updateTrack,
  deleteTrack,
  getTrendingData,
  getSystemMetrics,
  getStoreConnectivity,
  getRealtimeMetrics,
  getAuditLogs,
  getNotifications,
  createNotification,
  updateNotification,
  deleteNotification,
  markNotificationAsRead,
  getSystemSettings,
  updateSystemSettings,
  // Advanced analytics functions
  getPlaysByTimeRange,
  getGenreAnalytics,
  getStorePerformance,
  getTrackPopularity,
  getUserEngagement,

} = require('../controllers/admin.controller');

// Analytics routes
router.get('/analytics/top-tracks', authenticate, authorizeAdmin, getTopTracks);
router.get('/analytics/store-summary', authenticate, authorizeAdmin, getStoreSummary);
router.get('/analytics/trending', authenticate, authorizeAdmin, getTrendingData);
router.get('/analytics/system-metrics', authenticate, authorizeAdmin, getSystemMetrics);
router.get('/analytics/store-connectivity', authenticate, authorizeAdmin, getStoreConnectivity);
router.get('/analytics/realtime-metrics', authenticate, authorizeAdmin, getRealtimeMetrics);

// Advanced analytics routes
router.get('/analytics/plays-by-time', authenticate, authorizeAdmin, getPlaysByTimeRange);
router.get('/analytics/genre-analytics', authenticate, authorizeAdmin, getGenreAnalytics);
router.get('/analytics/store-performance', authenticate, authorizeAdmin, getStorePerformance);
router.get('/analytics/track-popularity', authenticate, authorizeAdmin, getTrackPopularity);
router.get('/analytics/user-engagement', authenticate, authorizeAdmin, getUserEngagement);

// Store management routes
router.get('/stores', authenticate, authorizeAdmin, getAllStores);
router.post('/stores', authenticate, authorizeAdmin, createStore);
router.put('/stores/:id', authenticate, authorizeAdmin, updateStore);
router.delete('/stores/:id', authenticate, authorizeAdmin, deleteStore);

// User management routes
router.get('/users', authenticate, authorizeAdmin, getAllUsers);
router.get('/users/all', authenticate, authorizeAdmin, getAllUsersIncludingCompliance);
router.post('/users', authenticate, authorizeAdmin, createUser);
router.put('/users/:id', authenticate, authorizeAdmin, updateUser);
router.delete('/users/:id', authenticate, authorizeAdmin, deleteUser);

// Track management routes
router.get('/tracks', authenticate, authorizeAdmin, getAllTracksAdmin);
router.get('/tracks/:id/details', authenticate, authorizeAdmin, getTrackDetails);
router.get('/tracks/:id/report', authenticate, authorizeAdmin, generateTrackReport);
router.put('/tracks/:id', authenticate, authorizeAdmin, updateTrack);
router.delete('/tracks/:id', authenticate, authorizeAdmin, deleteTrack);



// Monitoring and logs routes - Only allow compliance officers and admins to access audit logs
router.get('/audit-logs', authenticate, authorizeAuditTrail, getAuditLogs);

// Notifications routes
router.get('/notifications', authenticate, authorizeAdmin, getNotifications);
router.post('/notifications', authenticate, authorizeAdmin, createNotification);
router.put('/notifications/:id', authenticate, authorizeAdmin, updateNotification);
router.delete('/notifications/:id', authenticate, authorizeAdmin, deleteNotification);
router.put('/notifications/:id/read', authenticate, markNotificationAsRead);

// Settings routes
router.get('/settings', authenticate, authorizeAdmin, getSystemSettings);
router.put('/settings', authenticate, authorizeAdmin, updateSystemSettings);
router.post('/settings/validate', authenticate, authorizeAdmin, require('../controllers/admin.controller').validateSystemSettings);

module.exports = router;