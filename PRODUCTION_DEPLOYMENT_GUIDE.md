# TrakSong Production Deployment Guide
## Auto-Scaling Architecture for 1M+ Concurrent Stores

### 🏗️ Architecture Overview

This deployment creates a highly scalable, auto-scaling infrastructure on AWS Cape Town (af-south-1) capable of handling 1+ million concurrent store connections with the following components:

- **Load Balancing**: Application Load Balancer with auto-scaling target groups
- **Compute**: ECS Fargate with auto-scaling (10-1000 API instances, 5-500 WebSocket instances)
- **Database**: MongoDB Atlas with sharding and read replicas
- **Caching**: ElastiCache Redis cluster for session management and real-time data
- **Storage**: S3 for audio files with CloudFront CDN for global distribution
- **Monitoring**: CloudWatch, ELK stack, and custom metrics
- **Security**: WAF, VPC, security groups, and SSL termination

### 📋 Prerequisites

1. **AWS Account** with administrative access
2. **Domain name** registered and managed in Route 53
3. **MongoDB Atlas account** (for managed database)
4. **Docker** installed locally
5. **Terraform** >= 1.0 installed
6. **AWS CLI** configured with appropriate credentials
7. **Git** for version control

### 🚀 Step-by-Step Deployment Process

#### Phase 1: Initial Setup

1. **<PERSON>lone and Prepare Repository**
```bash
git clone <your-repo-url>
cd TrakSong
```

2. **Set Up Environment Variables**
```bash
# Copy and configure environment files
cp .env.example .env.production

# Edit .env.production with production values
nano .env.production
```

Required environment variables:
```env
# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/traksong
JWT_SECRET=your-super-secure-jwt-secret-256-bits-minimum
REDIS_URL=redis://your-redis-cluster-endpoint:6379

# Email Configuration (Brevo)
EMAIL_SERVICE=brevo
EMAIL_HOST=smtp-relay.brevo.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-brevo-smtp-key
EMAIL_FROM=TrakSong System <<EMAIL>>

# Application
NODE_ENV=production
FRONTEND_URL=https://yourdomain.com
```

#### Phase 2: Infrastructure Deployment

3. **Initialize Terraform Backend**
```bash
# Create S3 bucket for Terraform state
aws s3 mb s3://traksong-terraform-state --region af-south-1

# Create DynamoDB table for state locking
aws dynamodb create-table \
    --table-name traksong-terraform-locks \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region af-south-1
```

4. **Configure Terraform Variables**
```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
nano terraform.tfvars
```

Sample terraform.tfvars:
```hcl
# General
project_name = "traksong"
environment = "production"
aws_region = "af-south-1"
domain_name = "yourdomain.com"
ssl_certificate_arn = "arn:aws:acm:af-south-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# Auto Scaling Configuration
api_min_capacity = 10
api_max_capacity = 1000
api_desired_capacity = 20
api_cpu = 2048
api_memory = 4096

websocket_min_capacity = 5
websocket_max_capacity = 500
websocket_desired_capacity = 10

worker_min_capacity = 2
worker_max_capacity = 100
worker_desired_capacity = 5

# Database
mongodb_atlas_project_id = "your-atlas-project-id"
mongodb_instance_size = "M40"
mongodb_disk_size = 500

# Application Secrets
jwt_secret = "your-super-secure-jwt-secret"
email_user = "<EMAIL>"
email_password = "your-brevo-smtp-key"
alert_email = "<EMAIL>"
```

5. **Deploy Infrastructure**
```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan

# Apply infrastructure
terraform apply
```

#### Phase 3: Container Registry Setup

6. **Build and Push Docker Images**
```bash
# Get ECR login token
aws ecr get-login-password --region af-south-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.af-south-1.amazonaws.com

# Build and push API image
docker build -f Dockerfile.api -t traksong-api:latest .
docker tag traksong-api:latest <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:api-latest
docker push <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:api-latest

# Build and push UI image
docker build -f Dockerfile.ui -t traksong-ui:latest .
docker tag traksong-ui:latest <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:ui-latest
docker push <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:ui-latest

# Build and push WebSocket image
docker build -f Dockerfile.websocket -t traksong-websocket:latest .
docker tag traksong-websocket:latest <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:websocket-latest
docker push <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:websocket-latest

# Build and push Worker image
docker build -f Dockerfile.worker -t traksong-worker:latest .
docker tag traksong-worker:latest <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:worker-latest
docker push <account-id>.dkr.ecr.af-south-1.amazonaws.com/traksong:worker-latest
```

#### Phase 4: Database Setup

7. **Configure MongoDB Atlas**
```bash
# Connect to your MongoDB Atlas cluster and run initial setup
mongosh "mongodb+srv://cluster.mongodb.net/traksong" --username <username>

# Create indexes for performance
use traksong
db.playhistories.createIndex({ "storeId": 1, "playedDate": -1 })
db.playhistories.createIndex({ "trackId": 1, "playedDate": -1 })
db.tracks.createIndex({ "artist": 1, "title": 1 })
db.stores.createIndex({ "businessType": 1, "city": 1 })
```

8. **Seed Initial Data**
```bash
# Run database seeding from one of the API containers
aws ecs run-task \
    --cluster traksong-production-cluster \
    --task-definition traksong-production-api \
    --overrides '{"containerOverrides":[{"name":"api","command":["npm","run","seed"]}]}'
```

#### Phase 5: DNS and SSL Configuration

9. **Configure Route 53**
```bash
# Create hosted zone (if not exists)
aws route53 create-hosted-zone --name yourdomain.com --caller-reference $(date +%s)

# Create A record pointing to CloudFront distribution
# (This will be output from Terraform)
```

10. **Verify SSL Certificate**
```bash
# Check certificate status
aws acm describe-certificate --certificate-arn <your-certificate-arn> --region af-south-1
```

#### Phase 6: Monitoring and Alerts Setup

11. **Configure CloudWatch Dashboards**
```bash
# Import custom dashboard
aws cloudwatch put-dashboard --dashboard-name "TrakSong-Production" --dashboard-body file://cloudwatch-dashboard.json
```

12. **Set Up Log Aggregation**
```bash
# Configure log forwarding to ELK stack (if using)
# This is handled automatically by the ECS task definitions
```

### 🔧 Post-Deployment Configuration

#### Application Configuration

1. **Create Admin User**
```bash
# Access API container and create admin user
aws ecs execute-command \
    --cluster traksong-production-cluster \
    --task <task-id> \
    --container api \
    --interactive \
    --command "/bin/sh"

# Inside container:
npm run create-admin
```

2. **Configure Compliance Settings**
- Access admin dashboard at https://yourdomain.com/admin
- Configure SAMRO/SAMPRA API credentials
- Set up compliance reporting schedules
- Configure venue licenses

3. **Upload Initial Track Library**
- Use the admin interface to upload initial tracks
- Ensure all tracks have proper compliance metadata
- Set up initial playlists and schedules

#### Performance Tuning

1. **Auto Scaling Policies**
```bash
# Monitor initial performance and adjust scaling policies
aws application-autoscaling describe-scaling-policies \
    --service-namespace ecs \
    --resource-id service/traksong-production-cluster/traksong-production-api
```

2. **Database Performance**
- Monitor MongoDB Atlas metrics
- Adjust read preference for read-heavy operations
- Configure appropriate sharding keys if needed

3. **CDN Configuration**
- Configure CloudFront cache behaviors
- Set up origin failover for high availability
- Monitor cache hit ratios

### 📊 Monitoring and Maintenance

#### Key Metrics to Monitor

1. **Application Metrics**
   - API response times
   - WebSocket connection count
   - Active store connections
   - Track play rates
   - Error rates

2. **Infrastructure Metrics**
   - ECS service CPU/Memory utilization
   - Auto scaling events
   - Load balancer metrics
   - Database performance
   - Cache hit rates

3. **Business Metrics**
   - Concurrent store count
   - Daily/monthly active stores
   - Compliance reporting status
   - Revenue tracking

#### Maintenance Tasks

1. **Daily**
   - Check application health dashboards
   - Review error logs
   - Monitor auto scaling events

2. **Weekly**
   - Review performance metrics
   - Check database performance
   - Update security patches

3. **Monthly**
   - Review costs and optimize
   - Update dependencies
   - Backup verification
   - Disaster recovery testing

### 🚨 Troubleshooting Common Issues

#### High CPU Usage
```bash
# Scale up immediately if needed
aws application-autoscaling put-scaling-policy \
    --policy-name traksong-api-scale-up \
    --service-namespace ecs \
    --resource-id service/traksong-production-cluster/traksong-production-api \
    --scalable-dimension ecs:service:DesiredCount \
    --policy-type TargetTrackingScaling
```

#### Database Connection Issues
```bash
# Check MongoDB Atlas status
# Verify network access lists
# Check connection string configuration
```

#### WebSocket Connection Problems
```bash
# Check Network Load Balancer health
# Verify security group rules
# Monitor WebSocket service logs
```

### 🔄 Deployment Updates

#### Rolling Updates
```bash
# Update API service
aws ecs update-service \
    --cluster traksong-production-cluster \
    --service traksong-production-api \
    --task-definition traksong-production-api:NEW_REVISION

# Monitor deployment
aws ecs describe-services \
    --cluster traksong-production-cluster \
    --services traksong-production-api
```

#### Blue-Green Deployments
```bash
# Use Terraform to create new environment
terraform workspace new blue
terraform apply

# Switch traffic using Route 53 weighted routing
# Gradually shift traffic from green to blue
```

This deployment guide provides a comprehensive approach to scaling TrakSong for 1+ million concurrent stores while maintaining high availability, performance, and compliance requirements.

## 💰 Monthly Cost Estimation (AWS Cape Town Region)

### Base Infrastructure Costs

#### Compute (ECS Fargate)
- **API Services**: 20-1000 instances
  - Base: 20 × 2 vCPU × 4GB × $0.04048/vCPU-hour × 730 hours = $1,182/month
  - Peak: 1000 × 2 vCPU × 4GB × $0.04048/vCPU-hour × 100 hours = $8,096/month
  - **Average: $3,500/month**

- **WebSocket Services**: 10-500 instances
  - Base: 10 × 1 vCPU × 2GB × $0.04048/vCPU-hour × 730 hours = $295/month
  - Peak: 500 × 1 vCPU × 2GB × $0.04048/vCPU-hour × 100 hours = $2,024/month
  - **Average: $800/month**

- **Worker Services**: 5-100 instances
  - Base: 5 × 1 vCPU × 2GB × $0.04048/vCPU-hour × 730 hours = $148/month
  - Peak: 100 × 1 vCPU × 2GB × $0.04048/vCPU-hour × 100 hours = $405/month
  - **Average: $250/month**

**Total Compute: $4,550/month**

#### Database (MongoDB Atlas)
- **M40 Cluster** (3 nodes): $1,200/month
- **Read Replicas** (2 × M30): $600/month
- **Backup Storage** (500GB): $150/month
- **Data Transfer**: $200/month
- **Total Database: $2,150/month**

#### Caching (ElastiCache Redis)
- **cache.r7g.xlarge** (3 nodes): 3 × $0.419/hour × 730 hours = $917/month
- **Backup Storage**: $50/month
- **Total Caching: $967/month**

#### Storage & CDN
- **S3 Storage** (10TB audio files): $230/month
- **S3 Requests** (1M requests/day): $150/month
- **CloudFront** (100TB transfer): $8,500/month
- **Total Storage & CDN: $8,880/month**

#### Networking
- **Application Load Balancer**: $22/month + $0.008/LCU-hour × 730 × 100 = $606/month
- **Network Load Balancer**: $22/month + $0.006/NLCU-hour × 730 × 50 = $241/month
- **NAT Gateway** (3 AZs): 3 × $45/month = $135/month
- **Data Transfer**: $500/month
- **Total Networking: $1,504/month**

#### Monitoring & Logging
- **CloudWatch Logs** (1TB/month): $500/month
- **CloudWatch Metrics**: $300/month
- **CloudWatch Dashboards**: $30/month
- **Total Monitoring: $830/month**

#### Security & Compliance
- **AWS WAF**: $100/month
- **SSL Certificates**: $0 (AWS Certificate Manager)
- **Secrets Manager**: $50/month
- **Total Security: $150/month**

### Total Monthly Cost Breakdown

| Component | Monthly Cost (USD) |
|-----------|-------------------|
| Compute (ECS Fargate) | $4,550 |
| Database (MongoDB Atlas) | $2,150 |
| Caching (Redis) | $967 |
| Storage & CDN | $8,880 |
| Networking | $1,504 |
| Monitoring & Logging | $830 |
| Security & Compliance | $150 |
| **TOTAL** | **$19,031** |

### Cost Optimization Strategies

1. **Reserved Instances**: Save 30-50% on predictable workloads
2. **Spot Instances**: Use for non-critical worker processes (save 70%)
3. **S3 Intelligent Tiering**: Automatic cost optimization for storage
4. **CloudFront Optimization**: Optimize cache behaviors and compression
5. **Auto Scaling**: Aggressive scaling policies during low usage periods

### Estimated Cost at Different Scales

| Concurrent Stores | Monthly Cost | Cost per Store |
|------------------|--------------|----------------|
| 100,000 | $8,500 | $0.085 |
| 500,000 | $15,000 | $0.030 |
| 1,000,000 | $19,031 | $0.019 |
| 2,000,000 | $28,000 | $0.014 |

### Revenue Model Considerations

With 1M stores paying an average of $50/month for the service:
- **Monthly Revenue**: $50,000,000
- **Infrastructure Cost**: $19,031 (0.038% of revenue)
- **Gross Margin**: 99.96%

This demonstrates excellent scalability economics where infrastructure costs become negligible as scale increases.
