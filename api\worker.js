const dotenv = require('dotenv');
const mongoose = require('mongoose');
const Redis = require('redis');
const cron = require('cron');

// Load environment variables
dotenv.config();

// Import models and services
const PlayHistory = require('./models/PlayHistory.model');
const Track = require('./models/Track.model');
const Store = require('./models/Store.model');
const User = require('./models/User.model');
const complianceService = require('./services/compliance.service');
const emailService = require('./services/email.service');

// Redis client for job queues
const redis = Redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

// Connect to databases
async function connectDatabases() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Worker: MongoDB connected');
    
    // Connect to Redis
    await redis.connect();
    console.log('Worker: Redis connected');
    
  } catch (error) {
    console.error('Worker: Database connection error:', error);
    process.exit(1);
  }
}

// Job processors
class JobProcessor {
  constructor() {
    this.isProcessing = false;
  }
  
  async start() {
    console.log('Worker: Starting job processor...');
    this.isProcessing = true;
    
    // Process different types of jobs
    this.processComplianceReports();
    this.processEmailQueue();
    this.processAnalyticsAggregation();
    this.processCleanupTasks();
    
    // Set up cron jobs
    this.setupCronJobs();
  }
  
  async stop() {
    console.log('Worker: Stopping job processor...');
    this.isProcessing = false;
  }
  
  // Process compliance reporting jobs
  async processComplianceReports() {
    while (this.isProcessing) {
      try {
        const job = await redis.blPop('compliance_reports', 10); // 10 second timeout
        
        if (job) {
          const jobData = JSON.parse(job.element);
          console.log('Processing compliance report job:', jobData.id);
          
          await this.handleComplianceReport(jobData);
        }
      } catch (error) {
        console.error('Error processing compliance report:', error);
        await this.sleep(5000); // Wait 5 seconds before retrying
      }
    }
  }
  
  // Process email queue
  async processEmailQueue() {
    while (this.isProcessing) {
      try {
        const job = await redis.blPop('email_queue', 10);
        
        if (job) {
          const emailData = JSON.parse(job.element);
          console.log('Processing email job:', emailData.to);
          
          await emailService.sendEmail(emailData);
        }
      } catch (error) {
        console.error('Error processing email:', error);
        await this.sleep(5000);
      }
    }
  }
  
  // Process analytics aggregation
  async processAnalyticsAggregation() {
    while (this.isProcessing) {
      try {
        const job = await redis.blPop('analytics_aggregation', 10);
        
        if (job) {
          const aggregationData = JSON.parse(job.element);
          console.log('Processing analytics aggregation:', aggregationData.type);
          
          await this.handleAnalyticsAggregation(aggregationData);
        }
      } catch (error) {
        console.error('Error processing analytics aggregation:', error);
        await this.sleep(5000);
      }
    }
  }
  
  // Process cleanup tasks
  async processCleanupTasks() {
    while (this.isProcessing) {
      try {
        const job = await redis.blPop('cleanup_tasks', 10);
        
        if (job) {
          const cleanupData = JSON.parse(job.element);
          console.log('Processing cleanup task:', cleanupData.type);
          
          await this.handleCleanupTask(cleanupData);
        }
      } catch (error) {
        console.error('Error processing cleanup task:', error);
        await this.sleep(5000);
      }
    }
  }
  
  // Handle compliance report generation
  async handleComplianceReport(jobData) {
    const { storeId, reportType, dateRange } = jobData;
    
    try {
      // Generate compliance report
      const report = await complianceService.generateReport(storeId, reportType, dateRange);
      
      // Save report to database/S3
      await this.saveReport(report);
      
      // Notify completion
      await redis.publish('compliance_report_completed', JSON.stringify({
        storeId,
        reportId: report.id,
        status: 'completed'
      }));
      
      console.log(`Compliance report completed for store ${storeId}`);
      
    } catch (error) {
      console.error(`Error generating compliance report for store ${storeId}:`, error);
      
      // Notify failure
      await redis.publish('compliance_report_failed', JSON.stringify({
        storeId,
        error: error.message
      }));
    }
  }
  
  // Handle analytics aggregation
  async handleAnalyticsAggregation(aggregationData) {
    const { type, dateRange, storeIds } = aggregationData;
    
    try {
      switch (type) {
        case 'daily_summary':
          await this.generateDailySummary(dateRange, storeIds);
          break;
        case 'weekly_summary':
          await this.generateWeeklySummary(dateRange, storeIds);
          break;
        case 'monthly_summary':
          await this.generateMonthlySummary(dateRange, storeIds);
          break;
        default:
          console.log(`Unknown aggregation type: ${type}`);
      }
    } catch (error) {
      console.error(`Error in analytics aggregation (${type}):`, error);
    }
  }
  
  // Handle cleanup tasks
  async handleCleanupTask(cleanupData) {
    const { type, parameters } = cleanupData;
    
    try {
      switch (type) {
        case 'old_logs':
          await this.cleanupOldLogs(parameters.daysOld);
          break;
        case 'temp_files':
          await this.cleanupTempFiles();
          break;
        case 'expired_sessions':
          await this.cleanupExpiredSessions();
          break;
        default:
          console.log(`Unknown cleanup type: ${type}`);
      }
    } catch (error) {
      console.error(`Error in cleanup task (${type}):`, error);
    }
  }
  
  // Set up scheduled cron jobs
  setupCronJobs() {
    // Daily compliance report generation (2 AM)
    new cron.CronJob('0 2 * * *', async () => {
      console.log('Running daily compliance report generation...');
      await this.scheduleDailyComplianceReports();
    }, null, true, 'Africa/Johannesburg');
    
    // Weekly analytics aggregation (Sunday 3 AM)
    new cron.CronJob('0 3 * * 0', async () => {
      console.log('Running weekly analytics aggregation...');
      await this.scheduleWeeklyAnalytics();
    }, null, true, 'Africa/Johannesburg');
    
    // Monthly cleanup (1st of month, 4 AM)
    new cron.CronJob('0 4 1 * *', async () => {
      console.log('Running monthly cleanup...');
      await this.scheduleMonthlyCleanup();
    }, null, true, 'Africa/Johannesburg');
    
    // Hourly health check
    new cron.CronJob('0 * * * *', async () => {
      await this.performHealthCheck();
    }, null, true, 'Africa/Johannesburg');
  }
  
  // Schedule daily compliance reports for all stores
  async scheduleDailyComplianceReports() {
    try {
      const stores = await Store.find({ isActive: true });
      
      for (const store of stores) {
        await redis.lPush('compliance_reports', JSON.stringify({
          id: `daily_${store._id}_${Date.now()}`,
          storeId: store._id,
          reportType: 'daily',
          dateRange: {
            start: new Date(Date.now() - 24 * 60 * 60 * 1000),
            end: new Date()
          }
        }));
      }
      
      console.log(`Scheduled daily compliance reports for ${stores.length} stores`);
    } catch (error) {
      console.error('Error scheduling daily compliance reports:', error);
    }
  }
  
  // Generate daily summary analytics
  async generateDailySummary(dateRange, storeIds) {
    const summary = await PlayHistory.aggregate([
      {
        $match: {
          playedDate: { $gte: dateRange.start, $lte: dateRange.end },
          ...(storeIds && { storeId: { $in: storeIds } })
        }
      },
      {
        $group: {
          _id: {
            storeId: '$storeId',
            date: { $dateToString: { format: '%Y-%m-%d', date: '$playedDate' } }
          },
          totalPlays: { $sum: 1 },
          uniqueTracks: { $addToSet: '$trackId' },
          totalDuration: { $sum: '$durationPlayed' }
        }
      }
    ]);
    
    // Store aggregated data
    for (const item of summary) {
      await redis.setEx(
        `analytics:daily:${item._id.storeId}:${item._id.date}`,
        86400, // 24 hours
        JSON.stringify({
          totalPlays: item.totalPlays,
          uniqueTracks: item.uniqueTracks.length,
          totalDuration: item.totalDuration
        })
      );
    }
  }
  
  // Cleanup old logs
  async cleanupOldLogs(daysOld) {
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    
    // Clean up old play history (keep for compliance)
    // Only clean up non-compliance related data
    const result = await PlayHistory.deleteMany({
      playedDate: { $lt: cutoffDate },
      'compliance.requiresReporting': false
    });
    
    console.log(`Cleaned up ${result.deletedCount} old log entries`);
  }
  
  // Perform health check
  async performHealthCheck() {
    try {
      // Check database connection
      await mongoose.connection.db.admin().ping();
      
      // Check Redis connection
      await redis.ping();
      
      // Check queue sizes
      const queueSizes = {
        compliance_reports: await redis.lLen('compliance_reports'),
        email_queue: await redis.lLen('email_queue'),
        analytics_aggregation: await redis.lLen('analytics_aggregation'),
        cleanup_tasks: await redis.lLen('cleanup_tasks')
      };
      
      console.log('Worker health check passed. Queue sizes:', queueSizes);
      
      // Alert if queues are too large
      for (const [queue, size] of Object.entries(queueSizes)) {
        if (size > 1000) {
          console.warn(`Warning: Queue ${queue} has ${size} items`);
        }
      }
      
    } catch (error) {
      console.error('Worker health check failed:', error);
    }
  }
  
  // Utility function
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Save report (placeholder - implement based on your storage strategy)
  async saveReport(report) {
    // Implementation depends on whether you're using S3, local storage, etc.
    console.log('Report saved:', report.id);
  }
}

// Initialize and start worker
async function startWorker() {
  console.log('Starting TrakSong Worker...');
  
  await connectDatabases();
  
  const processor = new JobProcessor();
  await processor.start();
  
  // Graceful shutdown
  process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down worker...');
    await processor.stop();
    await redis.disconnect();
    await mongoose.disconnect();
    process.exit(0);
  });
  
  process.on('SIGINT', async () => {
    console.log('Received SIGINT, shutting down worker...');
    await processor.stop();
    await redis.disconnect();
    await mongoose.disconnect();
    process.exit(0);
  });
}

// Start the worker
startWorker().catch(error => {
  console.error('Failed to start worker:', error);
  process.exit(1);
});
