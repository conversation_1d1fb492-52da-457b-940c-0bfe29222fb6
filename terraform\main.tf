# TrakSong AWS Infrastructure
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "traksong-terraform-state"
    key    = "production/terraform.tfstate"
    region = "af-south-1"
    encrypt = true
    dynamodb_table = "traksong-terraform-locks"
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "TrakSong"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
  
  azs = slice(data.aws_availability_zones.available.names, 0, 3)
}

# VPC and Networking
module "vpc" {
  source = "./modules/vpc"
  
  name_prefix = local.name_prefix
  cidr_block  = var.vpc_cidr
  azs         = local.azs
  
  tags = local.common_tags
}

# Security Groups
module "security_groups" {
  source = "./modules/security"
  
  name_prefix = local.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  tags = local.common_tags
}

# Application Load Balancer
module "alb" {
  source = "./modules/alb"
  
  name_prefix         = local.name_prefix
  vpc_id              = module.vpc.vpc_id
  public_subnet_ids   = module.vpc.public_subnet_ids
  security_group_ids  = [module.security_groups.alb_security_group_id]
  certificate_arn     = var.ssl_certificate_arn
  
  tags = local.common_tags
}

# ECS Cluster
module "ecs" {
  source = "./modules/ecs"
  
  name_prefix                = local.name_prefix
  vpc_id                     = module.vpc.vpc_id
  private_subnet_ids         = module.vpc.private_subnet_ids
  alb_target_group_arn       = module.alb.api_target_group_arn
  websocket_target_group_arn = module.alb.websocket_target_group_arn
  security_group_ids         = [module.security_groups.ecs_security_group_id]
  
  # Auto Scaling Configuration
  api_min_capacity     = var.api_min_capacity
  api_max_capacity     = var.api_max_capacity
  api_desired_capacity = var.api_desired_capacity
  
  websocket_min_capacity     = var.websocket_min_capacity
  websocket_max_capacity     = var.websocket_max_capacity
  websocket_desired_capacity = var.websocket_desired_capacity
  
  worker_min_capacity     = var.worker_min_capacity
  worker_max_capacity     = var.worker_max_capacity
  worker_desired_capacity = var.worker_desired_capacity
  
  # Container Configuration
  api_cpu       = var.api_cpu
  api_memory    = var.api_memory
  websocket_cpu = var.websocket_cpu
  websocket_memory = var.websocket_memory
  worker_cpu    = var.worker_cpu
  worker_memory = var.worker_memory
  
  # Environment Variables
  environment_variables = {
    NODE_ENV     = var.environment
    MONGO_URI    = module.mongodb.connection_string
    REDIS_URL    = module.redis.connection_string
    JWT_SECRET   = var.jwt_secret
    EMAIL_SERVICE = var.email_service
    EMAIL_HOST   = var.email_host
    EMAIL_PORT   = var.email_port
    EMAIL_USER   = var.email_user
    EMAIL_PASSWORD = var.email_password
    FRONTEND_URL = "https://${var.domain_name}"
  }
  
  tags = local.common_tags
}

# ElastiCache Redis
module "redis" {
  source = "./modules/redis"
  
  name_prefix        = local.name_prefix
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  security_group_ids = [module.security_groups.redis_security_group_id]
  
  node_type          = var.redis_node_type
  num_cache_nodes    = var.redis_num_nodes
  parameter_group    = var.redis_parameter_group
  
  tags = local.common_tags
}

# MongoDB Atlas (External)
module "mongodb" {
  source = "./modules/mongodb"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # MongoDB Atlas Configuration
  mongodb_atlas_project_id = var.mongodb_atlas_project_id
  mongodb_cluster_name     = "${local.name_prefix}-cluster"
  mongodb_instance_size    = var.mongodb_instance_size
  mongodb_disk_size        = var.mongodb_disk_size
  mongodb_backup_enabled   = var.mongodb_backup_enabled
  
  # Network Access
  vpc_id = module.vpc.vpc_id
  private_subnet_cidrs = module.vpc.private_subnet_cidrs
  
  tags = local.common_tags
}

# S3 Buckets
module "s3" {
  source = "./modules/s3"
  
  name_prefix = local.name_prefix
  
  # Audio files bucket
  audio_bucket_name = "${local.name_prefix}-audio-files"
  
  # Reports bucket
  reports_bucket_name = "${local.name_prefix}-reports"
  
  # Backups bucket
  backups_bucket_name = "${local.name_prefix}-backups"
  
  tags = local.common_tags
}

# CloudFront CDN
module "cloudfront" {
  source = "./modules/cloudfront"
  
  name_prefix = local.name_prefix
  domain_name = var.domain_name
  
  # Origins
  alb_domain_name = module.alb.dns_name
  s3_bucket_domain = module.s3.audio_bucket_domain
  
  # SSL Certificate
  certificate_arn = var.ssl_certificate_arn
  
  tags = local.common_tags
}

# Auto Scaling Policies
module "autoscaling" {
  source = "./modules/autoscaling"
  
  name_prefix = local.name_prefix
  
  # ECS Service ARNs
  api_service_name       = module.ecs.api_service_name
  websocket_service_name = module.ecs.websocket_service_name
  worker_service_name    = module.ecs.worker_service_name
  
  cluster_name = module.ecs.cluster_name
  
  # Scaling Configuration
  api_scale_up_threshold   = var.api_scale_up_threshold
  api_scale_down_threshold = var.api_scale_down_threshold
  
  websocket_scale_up_threshold   = var.websocket_scale_up_threshold
  websocket_scale_down_threshold = var.websocket_scale_down_threshold
  
  tags = local.common_tags
}

# Monitoring and Logging
module "monitoring" {
  source = "./modules/monitoring"
  
  name_prefix = local.name_prefix
  
  # CloudWatch Log Groups
  api_log_group_name       = "/ecs/${local.name_prefix}-api"
  websocket_log_group_name = "/ecs/${local.name_prefix}-websocket"
  worker_log_group_name    = "/ecs/${local.name_prefix}-worker"
  
  # SNS Topics for Alerts
  alert_email = var.alert_email
  
  tags = local.common_tags
}
