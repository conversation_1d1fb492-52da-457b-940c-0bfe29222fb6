import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Badge,
  InputAdornment,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Message as MessageIcon,
  Send as SendIcon,
  Reply as ReplyIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Add as AddIcon,
  Store as StoreIcon,
  Person as PersonIcon,
  Inbox as InboxIcon,
  Outbox as OutboxIcon,
  Drafts as DraftsIcon,
  Archive as ArchiveIcon,
  PriorityHigh as PriorityIcon,
  AttachFile as AttachFileIcon,
  Download as DownloadIcon,
  AdminPanelSettings as AdminIcon,
  Security as ComplianceIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { messageService } from '../services/api';

const MaterialMessages = () => {
  const { user, isAdmin, isCompliance } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeTab, setActiveTab] = useState(0);
  const [messages, setMessages] = useState([
    {
      id: 1,
      subject: 'New Playlist Requirements',
      content: 'Please update the summer playlist with the latest tracks.',
      sender: 'Admin',
      senderRole: 'admin',
      recipient: 'Downtown Music Store',
      recipientRole: 'store',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: 'high',
      type: 'inbox',
      status: 'unread'
    },
    {
      id: 2,
      subject: 'System Maintenance Notice',
      content: 'Scheduled maintenance on Sunday 2AM-4AM. Please ensure all devices are ready.',
      sender: 'Admin',
      senderRole: 'admin',
      recipient: 'All Stores',
      recipientRole: 'store',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: true,
      priority: 'medium',
      type: 'sent',
      status: 'delivered'
    },
    {
      id: 3,
      subject: 'Audio Quality Issue',
      content: 'We are experiencing some audio quality issues with Track ID #1234.',
      sender: 'Mall Music Hub',
      senderRole: 'store',
      recipient: 'Admin',
      recipientRole: 'admin',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      read: false,
      priority: 'high',
      type: 'inbox',
      status: 'unread'
    },
    {
      id: 4,
      subject: 'Compliance Report Request',
      content: 'Please provide the monthly compliance report for SAMRO.',
      sender: 'SAMRO Compliance',
      senderRole: 'compliance',
      recipient: 'Admin',
      recipientRole: 'admin',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
      read: true,
      priority: 'medium',
      type: 'inbox',
      status: 'read'
    }
  ]);
  const [searchTerm, setSearchTerm] = useState('');
  const [composeOpen, setComposeOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [viewOpen, setViewOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newMessage, setNewMessage] = useState({
    recipient: '',
    subject: '',
    content: '',
    priority: 'medium'
  });
  const [attachments, setAttachments] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [recipients, setRecipients] = useState([]);
  const [loadingRecipients, setLoadingRecipients] = useState(false);

  // Get user role from auth context
  const userRole = user?.role || 'admin';

  // Load messages and recipients on component mount
  useEffect(() => {
    loadMessages();
    loadRecipients();
  }, []);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await messageService.getMessages({ type: 'all' });
      const messagesData = response.data.data || [];

      // Normalize message data to ensure proper timestamp handling and string conversion
      const normalizedMessages = messagesData.map(message => ({
        ...message,
        id: message.id || message._id || Date.now(),
        subject: typeof message.subject === 'string' ? message.subject : String(message.subject || 'No Subject'),
        content: typeof message.content === 'string' ? message.content : String(message.content || ''),
        sender: typeof message.sender === 'string' ? message.sender :
                (message.sender?.name || message.sender?.displayName || String(message.sender || 'Unknown Sender')),
        recipient: typeof message.recipient === 'string' ? message.recipient :
                   (message.recipient?.name || message.recipient?.displayName || String(message.recipient || 'Unknown Recipient')),
        timestamp: message.timestamp ? new Date(message.timestamp) : new Date(),
        read: message.read !== undefined ? message.read : false,
        priority: typeof message.priority === 'string' ? message.priority : String(message.priority || 'medium'),
        type: typeof message.type === 'string' ? message.type : String(message.type || 'inbox'),
        status: typeof message.status === 'string' ? message.status : String(message.status || 'unread')
      }));

      setMessages(normalizedMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
      // Keep mock data as fallback
    } finally {
      setLoading(false);
    }
  };

  const loadRecipients = async () => {
    try {
      setLoadingRecipients(true);
      const response = await messageService.getUsersForMessaging();
      setRecipients(response.data.data || []);
    } catch (error) {
      console.error('Error loading recipients:', error);
      // Fallback to empty array
      setRecipients([]);
    } finally {
      setLoadingRecipients(false);
    }
  };

  // Convert recipients from API to dropdown options
  const recipientOptions = recipients.map(recipient => ({
    id: recipient.id,
    label: recipient.displayName || recipient.name || 'Unknown User',
    value: recipient.id,
    role: recipient.role,
    name: recipient.name,
    organization: recipient.organization,
    storeName: recipient.storeName
  })).filter(option => option.label && option.value);

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'read': return 'info';
      case 'unread': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    const validFiles = files.filter(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      return true;
    });

    setAttachments(prev => [...prev, ...validFiles]);
  };

  const removeAttachment = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSendMessage = async () => {
    if (!newMessage.recipient || !newMessage.subject || !newMessage.content) {
      return;
    }

    try {
      setSending(true);
      const selectedRecipient = recipientOptions.find(r => r.value === newMessage.recipient);

      if (!selectedRecipient) {
        alert('Please select a valid recipient');
        return;
      }

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('subject', newMessage.subject);
      formData.append('content', newMessage.content);
      formData.append('recipientId', selectedRecipient.id);
      formData.append('recipientName', selectedRecipient.name);
      formData.append('recipientRole', selectedRecipient.role);
      formData.append('priority', newMessage.priority);

      // Add attachments
      attachments.forEach(file => {
        formData.append('attachments', file);
      });

      await messageService.sendMessage(formData);

      // Reload messages to show the sent message
      await loadMessages();

      setNewMessage({ recipient: '', subject: '', content: '', priority: 'medium' });
      setAttachments([]);
      setComposeOpen(false);
    } catch (error) {
      console.error('Error sending message:', error);
      // Fallback to local state update
      const getSenderName = () => {
        switch (userRole) {
          case 'store':
            return user?.storeName || 'Store Manager';
          case 'compliance':
            return user?.organization === 'SAMRO' ? 'SAMRO Compliance' :
                   user?.organization === 'SAMPRA' ? 'SAMPRA Compliance' : 'Compliance Officer';
          case 'admin':
          default:
            return 'Admin';
        }
      };

      const selectedRecipient = recipientOptions.find(r => r.value === newMessage.recipient);

      const message = {
        id: Date.now(),
        ...newMessage,
        sender: getSenderName(),
        senderRole: userRole,
        recipientRole: selectedRecipient?.role || 'admin',
        timestamp: new Date(),
        read: false,
        type: 'sent',
        status: 'delivered'
      };
      setMessages([message, ...messages]);
      setNewMessage({ recipient: '', subject: '', content: '', priority: 'medium' });
      setComposeOpen(false);
    } finally {
      setSending(false);
    }
  };

  const handleViewMessage = (message) => {
    setSelectedMessage(message);
    setViewOpen(true);
    if (!message.read && message.type === 'inbox') {
      setMessages(messages.map(m =>
        m.id === message.id ? { ...m, read: true, status: 'read' } : m
      ));
    }
  };

  const handleDeleteMessage = (id) => {
    setMessages(messages.filter(m => m.id !== id));
  };

  const handleReplyMessage = (message) => {
    // Find the original sender in recipients list
    const originalSender = recipientOptions.find(r =>
      r.label === message.sender ||
      r.name === message.sender ||
      r.id === message.senderId
    );

    // Pre-populate the compose form for reply
    setNewMessage({
      recipient: originalSender ? originalSender.value : '',
      subject: message.subject.startsWith('Re: ') ? message.subject : `Re: ${message.subject}`,
      content: `\n\n--- Original Message ---\nFrom: ${message.sender}\nDate: ${message.timestamp ? new Date(message.timestamp).toLocaleString() : 'No date'}\nSubject: ${message.subject}\n\n${message.content}`,
      priority: 'medium'
    });

    // Close view dialog if open and open compose dialog
    setViewOpen(false);
    setComposeOpen(true);
  };

  const handleDownloadAttachment = async (messageId, filename) => {
    try {
      const response = await messageService.downloadAttachment(messageId, filename);

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading attachment:', error);
      alert('Failed to download attachment. Please try again.');
    }
  };

  const filteredMessages = messages.filter(message => {
    // Add safety checks for undefined values
    const subject = message.subject || '';
    const content = message.content || '';
    const sender = message.sender || '';

    const matchesSearch = subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sender.toLowerCase().includes(searchTerm.toLowerCase());

    switch (activeTab) {
      case 0: return message.type === 'inbox' && matchesSearch;
      case 1: return message.type === 'sent' && matchesSearch;
      case 2: return message.type === 'drafts' && matchesSearch;
      case 3: return message.type === 'archive' && matchesSearch;
      default: return matchesSearch;
    }
  });

  const unreadCount = messages.filter(m => !m.read && m.type === 'inbox').length;
  const totalMessages = messages.length;
  const sentMessages = messages.filter(m => m.type === 'sent').length;
  const highPriorityMessages = messages.filter(m => m.priority === 'high').length;

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: isMobile ? 'stretch' : 'center',
        mb: 4,
        gap: isMobile ? 2 : 0
      }}>
        <Box>
          <Typography variant={isMobile ? "h5" : "h4"} sx={{ fontWeight: 600, mb: 1 }}>
            Messages
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            {userRole === 'store'
              ? 'Communicate with administrators and compliance staff'
              : (userRole === 'samro_staff' || userRole === 'sampra_staff' || userRole === 'compliance_admin')
              ? 'Communicate with stores and administrators'
              : 'Communicate with stores and manage notifications'
            }
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setComposeOpen(true)}
          fullWidth={isMobile}
        >
          Compose Message
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={isMobile ? 2 : 3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InboxIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Unread</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                {unreadCount}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                New messages
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MessageIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Total</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                {totalMessages}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                All messages
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <OutboxIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Sent</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                {sentMessages}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Messages sent
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PriorityIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="h6">High Priority</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, color: 'error.main' }}>
                {highPriorityMessages}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Urgent messages
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Paper sx={{ p: isMobile ? 2 : 3, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search messages..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size={isMobile ? "small" : "medium"}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Messages */}
      <Paper sx={{ p: isMobile ? 2 : 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ mb: 3 }}
          variant={isMobile ? "scrollable" : "standard"}
          scrollButtons={isMobile ? "auto" : false}
        >
          <Tab
            label={
              <Badge badgeContent={unreadCount} color="error">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <InboxIcon sx={{ mr: 1 }} />
                  Inbox
                </Box>
              </Badge>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <OutboxIcon sx={{ mr: 1 }} />
                Sent
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DraftsIcon sx={{ mr: 1 }} />
                Drafts
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ArchiveIcon sx={{ mr: 1 }} />
                Archive
              </Box>
            }
          />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <List>
            {filteredMessages.map((message, index) => (
              <React.Fragment key={message.id}>
                <ListItem
                  sx={{
                    bgcolor: message.read ? 'transparent' : 'action.hover',
                    borderRadius: 1,
                    mb: 1,
                    cursor: 'pointer'
                  }}
                  onClick={() => handleViewMessage(message)}
                >
                  <ListItemIcon>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <StoreIcon />
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: message.read ? 400 : 600 }}>
                          {String(message.subject || 'No Subject')}
                        </Typography>
                        <Chip
                          label={String(message.priority || 'medium')}
                          size="small"
                          color={getPriorityColor(message.priority)}
                          sx={{ fontSize: 10 }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box component="div">
                        <Typography variant="body2" component="div" sx={{ color: 'text.secondary', mb: 0.5 }}>
                          From: {String(message.sender || 'Unknown Sender')}
                        </Typography>
                        <Typography variant="body2" component="div" sx={{ color: 'text.secondary' }}>
                          {String(message.content || '').substring(0, 100)}...
                        </Typography>
                        <Typography variant="caption" component="div" sx={{ color: 'text.disabled' }}>
                          {message.timestamp ? new Date(message.timestamp).toLocaleString() : 'No date'}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleReplyMessage(message);
                        }}
                      >
                        <ReplyIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteMessage(message.id);
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < filteredMessages.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <List>
            {filteredMessages.map((message, index) => (
              <React.Fragment key={message.id}>
                <ListItem sx={{ borderRadius: 1, mb: 1 }}>
                  <ListItemIcon>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <PersonIcon />
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {String(message.subject || 'No Subject')}
                        </Typography>
                        <Chip
                          label={String(message.status || 'unknown')}
                          size="small"
                          color={getStatusColor(message.status)}
                          sx={{ fontSize: 10 }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box component="div">
                        <Typography variant="body2" component="div" sx={{ color: 'text.secondary', mb: 0.5 }}>
                          To: {String(message.recipient || 'Unknown Recipient')}
                        </Typography>
                        <Typography variant="caption" component="div" sx={{ color: 'text.disabled' }}>
                          {message.timestamp ? new Date(message.timestamp).toLocaleString() : 'No date'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < filteredMessages.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <DraftsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ color: 'text.secondary' }}>
              No drafts found
            </Typography>
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <ArchiveIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ color: 'text.secondary' }}>
              No archived messages
            </Typography>
          </Box>
        </TabPanel>
      </Paper>

      {/* Compose Message Dialog */}
      <Dialog open={composeOpen} onClose={() => setComposeOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Compose New Message</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Recipient</InputLabel>
                <Select
                  value={newMessage.recipient}
                  onChange={(e) => setNewMessage({ ...newMessage, recipient: e.target.value })}
                >
                  {recipientOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {option.role === 'admin' && <AdminIcon sx={{ fontSize: 16 }} />}
                        {(option.role === 'samro_staff' || option.role === 'sampra_staff' || option.role === 'compliance_admin') && <ComplianceIcon sx={{ fontSize: 16 }} />}
                        {option.role === 'store' && <StoreIcon sx={{ fontSize: 16 }} />}
                        <Typography variant="body2" component="span">
                          {String(option.label)}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={newMessage.priority}
                  onChange={(e) => setNewMessage({ ...newMessage, priority: e.target.value })}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Subject"
                value={newMessage.subject}
                onChange={(e) => setNewMessage({ ...newMessage, subject: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Message"
                multiline
                rows={6}
                value={newMessage.content}
                onChange={(e) => setNewMessage({ ...newMessage, content: e.target.value })}
              />
            </Grid>
          </Grid>

          {/* Attachments Section */}
          {attachments.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Attachments ({attachments.length})
              </Typography>
              {attachments.map((file, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 1,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AttachFileIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">{String(file.name || 'Unknown file')}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatFileSize(file.size)}
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => removeAttachment(index)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setComposeOpen(false)}>Cancel</Button>
          <input
            type="file"
            multiple
            style={{ display: 'none' }}
            id="file-input"
            onChange={handleFileSelect}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.csv,.zip,.jpg,.jpeg,.png,.gif"
          />
          <label htmlFor="file-input">
            <Button
              component="span"
              startIcon={<AttachFileIcon />}
              variant="outlined"
              disabled={sending}
            >
              Attach File
            </Button>
          </label>
          <Button
            onClick={handleSendMessage}
            variant="contained"
            startIcon={<SendIcon />}
            disabled={sending || !newMessage.recipient || !newMessage.subject || !newMessage.content}
          >
            {sending ? 'Sending...' : 'Send Message'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Message Dialog */}
      <Dialog open={viewOpen} onClose={() => setViewOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {String(selectedMessage?.subject || 'Message')}
        </DialogTitle>
        <DialogContent>
          {selectedMessage && (
            <Box sx={{ pt: 2 }}>
              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>From:</strong> {String(selectedMessage.sender || 'Unknown Sender')}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>To:</strong> {String(selectedMessage.recipient || 'Unknown Recipient')}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Date:</strong> {selectedMessage.timestamp ? new Date(selectedMessage.timestamp).toLocaleString() : 'No date'}
                </Typography>
                <Typography variant="body2">
                  <strong>Priority:</strong>
                  <Chip
                    label={String(selectedMessage.priority || 'medium')}
                    size="small"
                    color={getPriorityColor(selectedMessage.priority)}
                    sx={{ ml: 1 }}
                  />
                </Typography>
              </Box>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap', mb: 2 }}>
                {String(selectedMessage.content || 'No content')}
              </Typography>

              {/* Attachments */}
              {selectedMessage.attachments && selectedMessage.attachments.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Attachments ({selectedMessage.attachments.length})
                  </Typography>
                  {selectedMessage.attachments.map((attachment, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 2,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        mb: 1,
                        bgcolor: 'grey.50'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AttachFileIcon sx={{ mr: 2, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {String(attachment.originalName || 'Unknown file')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatFileSize(attachment.size)} • {String(attachment.mimetype || 'Unknown type')}
                          </Typography>
                        </Box>
                      </Box>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleDownloadAttachment(selectedMessage._id, attachment.filename)}
                      >
                        Download
                      </Button>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewOpen(false)}>Close</Button>
          <Button
            startIcon={<ReplyIcon />}
            variant="outlined"
            onClick={() => handleReplyMessage(selectedMessage)}
          >
            Reply
          </Button>
          <Button
            startIcon={<DeleteIcon />}
            color="error"
            onClick={() => {
              handleDeleteMessage(selectedMessage.id);
              setViewOpen(false);
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialMessages;
