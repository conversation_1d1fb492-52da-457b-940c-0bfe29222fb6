version: '3.8'

services:
  # Load Balancer (HAProxy)
  loadbalancer:
    image: haproxy:2.8-alpine
    container_name: traksong-lb
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # HAProxy stats
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - api-1
      - api-2
      - ui
    networks:
      - traksong-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TrakSong API - Instance 1
  api-1:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: production
    container_name: traksong-api-1
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      INSTANCE_ID: api-1
      MONGO_URI: ${MONGO_URI}
      JWT_SECRET: ${JWT_SECRET}
      REDIS_URL: ${REDIS_URL}
      EMAIL_SERVICE: ${EMAIL_SERVICE}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
      EMAIL_FROM: ${EMAIL_FROM}
      FRONTEND_URL: ${FRONTEND_URL}
    volumes:
      - uploads_data:/app/uploads
      - reports_data:/app/reports
      - logs_data:/app/logs
    networks:
      - traksong-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # TrakSong API - Instance 2
  api-2:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: production
    container_name: traksong-api-2
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      INSTANCE_ID: api-2
      MONGO_URI: ${MONGO_URI}
      JWT_SECRET: ${JWT_SECRET}
      REDIS_URL: ${REDIS_URL}
      EMAIL_SERVICE: ${EMAIL_SERVICE}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
      EMAIL_FROM: ${EMAIL_FROM}
      FRONTEND_URL: ${FRONTEND_URL}
    volumes:
      - uploads_data:/app/uploads
      - reports_data:/app/reports
      - logs_data:/app/logs
    networks:
      - traksong-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # TrakSong UI
  ui:
    build:
      context: .
      dockerfile: Dockerfile.ui
      target: production
    container_name: traksong-ui
    restart: unless-stopped
    networks:
      - traksong-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # WebSocket Service (Separate from API)
  websocket:
    build:
      context: .
      dockerfile: Dockerfile.websocket
      target: production
    container_name: traksong-websocket
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 8080
      REDIS_URL: ${REDIS_URL}
      JWT_SECRET: ${JWT_SECRET}
    networks:
      - traksong-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Background Workers
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
      target: production
    container_name: traksong-worker
    restart: unless-stopped
    environment:
      NODE_ENV: production
      MONGO_URI: ${MONGO_URI}
      REDIS_URL: ${REDIS_URL}
      EMAIL_SERVICE: ${EMAIL_SERVICE}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
    networks:
      - traksong-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

volumes:
  uploads_data:
    driver: local
  reports_data:
    driver: local
  logs_data:
    driver: local

networks:
  traksong-network:
    driver: bridge
