# Multi-stage build for TrakSong UI
FROM node:18-alpine AS base

WORKDIR /app

# Copy package files
COPY ui/package*.json ./
RUN npm ci && npm cache clean --force

# Development stage
FROM base AS development
COPY ui/ .
EXPOSE 5173
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# Build stage
FROM base AS build
COPY ui/ .
RUN npm run build

# Production stage with Nginx
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy custom nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=build /app/dist /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S nginx && \
    adduser -S traksong -u 1001 -G nginx

# Set permissions
RUN chown -R traksong:nginx /usr/share/nginx/html && \
    chown -R traksong:nginx /var/cache/nginx && \
    chown -R traksong:nginx /var/log/nginx && \
    chown -R traksong:nginx /etc/nginx/conf.d

RUN touch /var/run/nginx.pid && \
    chown -R traksong:nginx /var/run/nginx.pid

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

USER traksong
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
