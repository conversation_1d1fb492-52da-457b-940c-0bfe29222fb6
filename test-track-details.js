// Test script for track details API endpoints
const mongoose = require('mongoose');
require('dotenv').config({ path: './.env' });

// Import models
const Track = require('./models/Track.model');
const PlayHistory = require('./models/PlayHistory.model');
const Store = require('./models/Store.model');

// Import controller functions
const { getTrackDetails, generateTrackReport } = require('./controllers/admin.controller');

async function testTrackDetails() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/traksong');
    console.log('Connected to MongoDB');

    // Find a track to test with
    const track = await Track.findOne();
    if (!track) {
      console.log('No tracks found in database');
      return;
    }

    console.log('Testing with track:', track.title, 'by', track.artist);

    // Mock request and response objects
    const req = {
      params: { id: track._id.toString() },
      query: {},
      user: { username: 'test-admin' }
    };

    const res = {
      json: (data) => {
        console.log('Track Details Response:', JSON.stringify(data, null, 2));
      },
      status: (code) => ({
        json: (data) => {
          console.log('Error Response:', code, data);
        }
      })
    };

    // Test getTrackDetails
    console.log('\n=== Testing getTrackDetails ===');
    await getTrackDetails(req, res);

    // Test generateTrackReport
    console.log('\n=== Testing generateTrackReport ===');
    await generateTrackReport(req, res);

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testTrackDetails();
