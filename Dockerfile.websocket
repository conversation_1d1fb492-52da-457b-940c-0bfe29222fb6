# WebSocket Service Dockerfile
FROM node:18-alpine AS base

WORKDIR /app

# Install dependencies for WebSocket service
RUN apk add --no-cache python3 make g++

# Copy package files
COPY api/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Development stage
FROM base AS development
RUN npm ci
COPY api/ .
EXPOSE 8080
CMD ["node", "websocket-server.js"]

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S traksong -u 1001

WORKDIR /app

# Install production dependencies
COPY api/package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/*

# Copy WebSocket server code
COPY api/websocket-server.js ./
COPY api/config/ ./config/
COPY api/models/ ./models/
COPY api/middleware/ ./middleware/
COPY api/services/ ./services/

# Create necessary directories
RUN mkdir -p logs && \
    chown -R traksong:nodejs logs

# Health check for WebSocket service
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "const WebSocket = require('ws'); const ws = new WebSocket('ws://localhost:8080'); ws.on('open', () => { ws.close(); process.exit(0); }); ws.on('error', () => process.exit(1));"

USER traksong
EXPOSE 8080

CMD ["node", "websocket-server.js"]
